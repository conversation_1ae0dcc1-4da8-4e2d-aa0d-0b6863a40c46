<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柠汐认证平台 | 注册</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow-x: hidden;
            position: relative;
        }

        /* 装饰元素 */
        .bubble {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(106, 17, 203, 0.05) 0%, rgba(37, 117, 252, 0.05) 100%);
            z-index: 0;
            animation: float 15s infinite ease-in-out;
        }

        .bubble:nth-child(1) {
            width: 150px;
            height: 150px;
            top: 10%;
            left: 5%;
            animation-delay: 0s;
        }

        .bubble:nth-child(2) {
            width: 220px;
            height: 220px;
            bottom: 15%;
            right: 10%;
            animation-delay: -5s;
        }

        .bubble:nth-child(3) {
            width: 100px;
            height: 100px;
            top: 50%;
            left: 20%;
            animation-delay: -10s;
        }

        .bubble:nth-child(4) {
            width: 180px;
            height: 180px;
            bottom: 30%;
            right: 25%;
            animation-delay: -8s;
        }

        .container {
            position: relative;
            width: 100%;
            max-width: 1200px;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
            width: 100%;
            max-width: 500px;
            padding: 50px 40px;
            z-index: 10;
            transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.4s ease;
            overflow: hidden;
            position: relative;
            animation: cardEntrance 0.8s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #6a11cb, #2575fc, #6a11cb);
            background-size: 200% 200%;
            animation: gradientLine 3s ease infinite;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeIn 0.8s 0.2s ease forwards;
            opacity: 0;
        }

        .logo {
            width: 120px;
            height: 120px;
            border-radius: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(106, 17, 203, 0.15);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: white;
            border: 1px solid rgba(106, 17, 203, 0.1);
        }

        .logo img {
            width: 80%;
            height: auto;
            transition: transform 0.5s ease;
        }

        .logo:hover {
            transform: scale(1.05) rotate(2deg);
            box-shadow: 0 12px 35px rgba(106, 17, 203, 0.25);
        }

        .logo:hover img {
            transform: scale(1.1);
        }

        .title {
            color: #333;
            font-size: 2.4rem;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            opacity: 0;
            animation: fadeIn 0.8s 0.3s ease forwards;
        }

        .subtitle {
            color: #777;
            font-size: 1.1rem;
            font-weight: 400;
            margin-bottom: 35px;
            text-align: center;
            opacity: 0;
            animation: fadeIn 0.8s 0.4s ease forwards;
        }

        .form-group {
            position: relative;
            margin-bottom: 32px;
            opacity: 0;
            animation: fadeInUp 0.6s 0.5s ease forwards;
        }

        .form-input {
            width: 100%;
            padding: 18px 20px;
            font-size: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 14px;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            outline: none;
            color: #444;
        }

        .form-input:focus {
            border-color: rgba(106, 17, 203, 0.5);
            box-shadow: 0 4px 20px rgba(106, 17, 203, 0.15);
            transform: translateY(-2px);
        }

        .form-label {
            position: absolute;
            top: 18px;
            left: 20px;
            color: #888;
            font-size: 16px;
            font-weight: 400;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            pointer-events: none;
        }

        .form-input:focus + .form-label,
        .form-input:not(:placeholder-shown) + .form-label {
            top: -12px;
            left: 15px;
            font-size: 13px;
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: white;
            padding: 3px 12px;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(106, 17, 203, 0.2);
        }

        .password-toggle {
            position: absolute;
            right: 18px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #888;
            transition: color 0.3s, transform 0.3s;
        }

        .password-toggle:hover {
            color: #6a11cb;
            transform: translateY(-50%) scale(1.1);
        }

        .btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            font-size: 18px;
            font-weight: 600;
            border: none;
            border-radius: 14px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 7px 20px rgba(37, 117, 252, 0.3);
            overflow: hidden;
            position: relative;
            letter-spacing: 0.5px;
            opacity: 0;
            animation: fadeIn 0.8s 0.7s ease forwards;
        }

        .btn:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(37, 117, 252, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -60%;
            width: 20px;
            height: 200%;
            background: rgba(255, 255, 255, 0.4);
            transform: rotate(30deg);
            transition: all 0.6s;
        }

        .btn:hover::after {
            left: 120%;
        }

        .btn i {
            margin-right: 10px;
            transition: transform 0.3s;
        }

        .btn:hover i {
            transform: scale(1.2);
        }

        .login-link {
            text-align: center;
            margin-top: 28px;
            color: #777;
            font-size: 15px;
            opacity: 0;
            animation: fadeIn 0.8s 0.8s ease forwards;
        }

        .login-link a {
            color: #6a11cb;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            position: relative;
            padding-bottom: 2px;
        }

        .login-link a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #6a11cb, #2575fc);
            transition: width 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .login-link a:hover::after {
            width: 100%;
        }

        .terms {
            display: flex;
            align-items: center;
            margin: 25px 0;
            color: #777;
            font-size: 14px;
            opacity: 0;
            animation: fadeIn 0.8s 0.6s ease forwards;
        }

        .terms input {
            margin-right: 12px;
            width: 20px;
            height: 20px;
            cursor: pointer;
            accent-color: #6a11cb;
            transition: transform 0.3s;
        }

        .terms input:hover {
            transform: scale(1.1);
        }

        .terms a {
            color: #6a11cb;
            text-decoration: none;
            font-weight: 600;
            margin-left: 5px;
            transition: all 0.3s;
        }

        .terms a:hover {
            text-decoration: underline;
        }

        /* 动画定义 */
        @keyframes float {
            0%, 100% {
                transform: translateY(0) translateX(0);
            }
            25% {
                transform: translateY(-20px) translateX(15px);
            }
            50% {
                transform: translateY(10px) translateX(-20px);
            }
            75% {
                transform: translateY(-15px) translateX(-10px);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes cardEntrance {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes gradientLine {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .card {
                padding: 40px 30px;
                max-width: 90%;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .logo {
                width: 100px;
                height: 100px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }
            
            .card {
                padding: 35px 25px;
                border-radius: 20px;
            }
            
            .title {
                font-size: 1.8rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .form-input {
                padding: 16px 18px;
            }
            
            .btn {
                padding: 16px;
                font-size: 16px;
            }
        }

        @media (max-width: 360px) {
            .card {
                padding: 30px 20px;
            }
            
            .title {
                font-size: 1.6rem;
            }
            
            .logo {
                width: 90px;
                height: 90px;
            }
        }

        /* 粒子背景 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            background: rgba(106, 17, 203, 0.05);
            animation: floatParticle 15s infinite linear;
        }
    </style>
</head>
<body>
    <!-- 背景装饰元素 -->
    <div class="bubble"></div>
    <div class="bubble"></div>
    <div class="bubble"></div>
    <div class="bubble"></div>
    
    <!-- 粒子背景 -->
    <div class="particles" id="particles"></div>
    
    <div class="container">
        <div class="card">
            <div class="logo-container">
                <div class="logo">
                    <img src="https://d.ly-y.cn/up/diugai.com175413109649212.png" alt="柠汐认证平台">
                </div>
                <h1 class="title">柠汐认证平台</h1>
                <p class="subtitle">加入我们，开启安全认证之旅</p>
            </div>
            
            <form id="registerForm">
                <div class="form-group">
                    <input type="text" class="form-input" id="username" placeholder=" " required>
                    <label for="username" class="form-label">用户名</label>
                </div>
                
                <div class="form-group">
                    <input type="email" class="form-input" id="email" placeholder=" " required>
                    <label for="email" class="form-label">电子邮箱</label>
                </div>
                
                <div class="form-group">
                    <input type="password" class="form-input" id="password" placeholder=" " required>
                    <label for="password" class="form-label">密码</label>
                    <span class="password-toggle" id="togglePassword">
                        <i class="far fa-eye"></i>
                    </span>
                </div>
                
                <div class="form-group">
                    <input type="password" class="form-input" id="confirmPassword" placeholder=" " required>
                    <label for="confirmPassword" class="form-label">确认密码</label>
                </div>
                
                <div class="terms">
                    <input type="checkbox" id="terms" required>
                    <label for="terms">我已阅读并同意<a href="#">服务条款</a>和<a href="#">隐私政策</a></label>
                </div>
                
                <button type="submit" class="btn">
                    <i class="fas fa-user-plus"></i> 立即注册
                </button>
            </form>
            
            <div class="login-link">
                已有账户？<a href="#">立即登录</a>
            </div>
        </div>
    </div>

    <script>
        // 创建粒子背景
        function createParticles() {
            const container = document.getElementById('particles');
            const particleCount = 30;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');
                
                // 随机大小
                const size = Math.random() * 60 + 20;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                
                // 随机位置
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                
                // 随机动画延迟
                const delay = Math.random() * 15;
                particle.style.animationDelay = `-${delay}s`;
                
                // 随机透明度
                const opacity = Math.random() * 0.1 + 0.05;
                particle.style.opacity = opacity;
                
                container.appendChild(particle);
            }
        }

        // 密码显示/隐藏切换
        const togglePassword = document.getElementById('togglePassword');
        const password = document.getElementById('password');
        const eyeIcon = togglePassword.querySelector('i');
        
        togglePassword.addEventListener('click', function() {
            const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
            password.setAttribute('type', type);
            eyeIcon.classList.toggle('fa-eye');
            eyeIcon.classList.toggle('fa-eye-slash');
            
            // 添加动画效果
            this.style.transform = 'translateY(-50%) scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'translateY(-50%)';
            }, 200);
        });
        
        // 表单提交动画
        const registerForm = document.getElementById('registerForm');
        
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const btn = this.querySelector('.btn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
            btn.style.pointerEvents = 'none';
            
            // 模拟注册过程
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-check"></i> 注册成功!';
                btn.style.background = 'linear-gradient(135deg, #2ecc71, #27ae60)';
                
                // 添加庆祝动画
                document.querySelectorAll('.form-group').forEach(group => {
                    group.style.transform = 'translateY(-5px)';
                    setTimeout(() => {
                        group.style.transform = 'translateY(0)';
                    }, 300);
                });
                
                // 重置按钮状态
                setTimeout(() => {
                    btn.innerHTML = '<i class="fas fa-user-plus"></i> 立即注册';
                    btn.style.background = 'linear-gradient(135deg, #6a11cb, #2575fc)';
                    btn.style.pointerEvents = 'auto';
                }, 2500);
            }, 1800);
        });
        
        // 输入框聚焦效果增强
        const inputs = document.querySelectorAll('.form-input');
        
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
        
        // 页面加载时创建粒子
        window.addEventListener('load', createParticles);
    </script>
</body>
</html>