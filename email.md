API服务地址
更新时间：2024-09-12 17:45:11
产品详情
我的收藏
符合以下使用限制说明场景的建议首选VPC Endpoint地址，这样可以节省您的访问邮件推送网络费用。

VPC Endpoint使用限制说明
1、使用相应地域VPC Endpoint的时候，需要从阿里云对应地域的ECS或者容器中访问，无法跨地域使用。 2、VPC Endpoint直接感知您的ECS或者容器的IP而非出口IP，开启了IP保护功能的存量客户如果使用，请到邮件推送控制台添加您的ECS或者容器IP到白名单列表中。

 
华东1（杭州）
RegionId: cn-hangzhou
公网:Endpoint: dm.aliyuncs.com
VPC:Endpoint: dm-vpc.cn-hangzhou.aliyuncs.com 
Version: 2015-11-23

新加坡
RegionId:ap-southeast-1
公网:Endpoint: dm.ap-southeast-1.aliyuncs.com
VPC:Endpoint: dm-vpc.ap-southeast-1.aliyuncs.com
Version: 2015-11-23

美国（原悉尼）
RegionId: ap-southeast-2
公网:Endpoint: dm.ap-southeast-2.aliyuncs.com
VPC:Endpoint: dm-vpc.ap-southeast-2.aliyuncs.com 
Version: 2015-11-23

美国（新），新用户若选择美国区域，推荐使用新服务器。
RegionId: us-east-1
公网:Endpoint: dm.us-east-1.aliyuncs.com
VPC:Endpoint: dm-vpc.us-east-1.aliyuncs.com 
Version: 2015-11-23

德国（法兰克福）
RegionId: eu-central-1
公网:Endpoint: dm.eu-central-1.aliyuncs.com
VPC:Endpoint: dm-vpc.eu-central-1.aliyuncs.com 
Version: 2015-11-23
说明
API服务地址的区域要和控制台配置的区域保持一致。

如何选择发信服务区域，请参考：如何选择发信区域


概述
更新时间：2024-08-08 13:58:45
产品详情
我的收藏
通过向 DirectMail API 的服务端地址发送 HTTP 的 GET 或 POST 请求，并按照接口说明在请求中加入相应请求参数来完成对 DirectMail API 接口调用。根据请求的处理情况，系统会返回处理结果。

详细信息，请参见：

请求结构

公共参数

返回结果

签名机制

请求结构
更新时间：2022-10-27 09:24:27
产品详情
我的收藏
服务地址
DirectMail API 的服务接入地址

通信协议
支持通过 HTTP 或 HTTPS 通道进行请求通信。为了获得更高的安全性，推荐您使用 HTTPS 通道发送请求。

请求方法
支持 HTTP GET 方法发送请求，这种方式下请求参数需要包含在请求的 URL 中。支持 HTTP POST 方法发送请求，这种方式下请求参数需要包含在请求的 BODY 中。

请求参数
每个请求都需要指定要执行的操作，即Action参数（例如 SingleSendMail），以及每个操作都需要包含的公共请求参数和指定操作所特有的请求参数。

字符编码
请求及返回结果都使用UTF-8字符集进行编码。

上一篇：概述


共参数
更新时间：2024-08-08 10:07:28
产品详情
我的收藏
公共请求参数
公共请求参数是指调用每个接口都需要使用到的请求参数。





名称

类型

是否必须

描述

Format

String

否

返回值的类型，支持 JSON 与 XML。默认为 XML。

Version

String

是

API 版本号，为日期形式：YYYY-MM-DD。如果参数 RegionID 是 cn-hangzhou，则版本对应为2015-11-23；如参数 RegionID 是cn-hangzhou 以外其他 Region，比如 ap-southeast-1，则版本对应为2017-06-22。

AccessKeyId

String

是

阿里云颁发给用户的访问服务所用的密钥 ID，可以通过控制台AccessKey管理来创建。

Signature

String

是

签名结果串，关于签名的计算方法，请参见签名机制。

SignatureMethod

String

是

签名方式，目前支持 HMAC-SHA1。

Timestamp

String

是

请求的时间戳。日期格式按照 ISO8601 标准表示，并需要使用 UTC 时间。格式为YYYY-MM-DDThh:mm:ssZ。 例如，2015-11-23T04:00:00Z（为北京时间 2015 年 11 月 23 日 12 点 0 分 0 秒）。

SignatureVersion

String

是

签名算法版本，目前版本是 1.0。

SignatureNonce

String

是

唯一随机数，用于防止网络重放攻击。不同的请求要使用不同的随机数值。您可以使用UUID（随机串），也可以自定义。

RegionId

String

否

机房信息。具体值请参考文档中的RegionId字段：API服务地址。

示例
 
https://dm.aliyuncs.com/
?Format=xml
&Version=2015-11-23
&Signature=Pc5WB8gokVn0xfeu%2FZV%2BiNM1dgI%3D 
&SignatureMethod=HMAC-SHA1
&SignatureNonce=e1b44502-6d13-4433-9493-69eeb068e955
&SignatureVersion=1.0
&AccessKeyId=key-test
&Timestamp=2015-11-23T12:00:00Z
公共返回参数
用户发送的每次接口调用请求，无论成功与否，系统都会返回一个唯一识别码 RequestId。

参数

类型

描述

RequestId

String

阿里云为该请求生成的唯一标识符。

示例
XML示例
 
<?xml version="1.0" encoding="UTF-8"?> 
<!-结果的根结点-->
<接口名称+Response>
    <!-返回请求标签-->
    <RequestId>4C467B38-3910-447D-87BC-AC049166F216</RequestId>
    <!-返回结果数据-->
</接口名称+Response>
JSON示例
 
{
    "RequestId": "4C467B38-3910-447D-87BC-AC049166F216"
    /* 返回结果数据 */
}

返回结果
更新时间：2024-08-08 13:58:26
产品详情
我的收藏
调用API服务后返回数据采用统一格式：

返回的 HTTP 状态码为 2xx，代表调用成功；

返回的 HTTP 状态码为 4xx 或 5xx 代表调用失败。

调用成功返回的数据格式主要有 XML 和 JSON 两种，外部系统可以在请求时传入参数来制定返回的数据格式，默认为 XML 格式。

说明
本文档中的返回示例为了便于用户查看，做了格式化处理，实际返回结果是没有进行换行、缩进等处理的。

成功结果
XML示例
 
<?xml version="1.0" encoding="UTF-8"?> 
<!-结果的根结点-->
<接口名称+Response>
    <!-返回请求标签-->
    <RequestId>4C467B38-3910-447D-87BC-AC049166F216</RequestId>
    <!-返回结果数据-->
</接口名称+Response>
JSON示例
 
{
    "RequestId": "4C467B38-3910-447D-87BC-AC049166F216"
    /* 返回结果数据 */
}
错误结果
调用接口出错后，将不会返回结果数据。调用方可根据每个接口对应的错误码以及 错误代码表 来定位错误原因。

当调用出错时，HTTP 请求返回一个 4xx 或 5xx 的 HTTP 状态码。返回的消息体中是具体的错误代码及错误信息。另外还包含一个全局唯一的请求 ID：RequestId 和一个您该次请求访问的站点 ID：HostId。在调用方找不到错误原因时，可以联系阿里云客服，并提供该 HostId 和 RequestId，以便我们尽快帮您解决问题。

XML示例
 
<?xml version="1.0" encoding="UTF-8"?>
<Error>
   <RequestId>8906582E-6722-409A-A6C4-0E7863B733A5</RequestId>
   <HostId>dm.aliyuncs.com</HostId>
   <Code>InvalidTemplate.NotFound</Code>
   <Message>The specified template does not found.</Message>
</Error>
JSON示例
 
{
    "RequestId": "8906582E-6722-409A-A6C4-0E7863B733A5",
    "HostId": "dm.aliyuncs.com",
    "Code": "InvalidTemplate.NotFound",
    "Message": "The specified template does not found."
}



签名机制
更新时间：2024-03-13 16:36:34
产品详情
我的收藏
注意：

若使用阿里云提供的 SDK，则无需查看签名机制。当前已提供 Java、PHP、C# 的 SDK。

接口支持通过 GET 和 POST 提交请求，但是 GET 和 POST 的 StringToSign 不一样。

邮件推送服务会对每个访问请求进行身份验证，所以无论使用 HTTP 还是 HTTPS 协议提交请求，都需要在请求中包含签名（Signature）信息。邮件推送通过使用 AccessKeyID 和 AccessKeySecret 进行对称加密的方法来验证请求的发送者身份。AccessKeyID 和 AccessKeySecret 由阿里云官方颁发给用户的 AccessKey 信息（可以通过阿里云控制台用户信息管理中查看和管理）。其中 AccessKeyID 用于标识访问者的身份；AccessKeySecret 是用于加密签名字符串和服务器端验证签名字符串的密钥。AccessKey 信息必须严格保密。

签名步骤
在访问时，按照下面的方法对请求进行签名处理：

使用请求参数构造规范化的请求字符串（Canonicalized Query String）

a) 排序参数。

按照参数名称的字典顺序，对请求中所有的请求参数（包括文档中描述的公共请求参数和给定的请求接口的自定义参数，但不包括公共请求参数中的 Signature 参数本身）进行排序。

注意：当使用 GET 方法提交请求时，这些参数就是请求 URI 中的参数部分（即 URI 中 ? 之后由 & 连接的部分）。

b) 对每个请求参数的名称和值进行编码。

参数名称和值要使用 UTF-8 字符集进行 URL 编码。URL 编码的编码规则是：

对于字符 A-Z、a-z、0-9 以及字符 -、_、.、~ 不编码。

对于其他字符编码成 %XY 的格式，其中 XY 是字符对应 ASCII 码的 16 进制表示。比如半角的双引号 ” 对应的编码就是 %22。

对于扩展的 UTF-8 字符，编码成 %XY%ZA… 的格式。

需要说明的是半角的空格要被编码是 %20，而不是加号 +。

特别注意：一般支持 URL 编码的库（比如 Java 中的 java.net.URLEncoder）都是按照 application/x-www-form-urlencoded 的 MIME 类型的规则进行编码。可以直接使用这类方式进行编码，把编码后的字符串中加号 + 替换成 %20、星号 * 替换成 %2A、%7E 替换回波浪号 ~，即可得到上述规则描述的编码字符串。

c) 使用半角的等号 = 连接编码后的参数名称和参数值。

d) 使用 & 符号连接编码后的请求参数（参数排序与步骤 a 的排序一致），即得到规范化请求字符串。

使用上一步构造的规范化字符串，按照下面的规则构造用于计算签名的字符串：

 
 StringToSign=
 HTTPMethod + "&" +
 percentEncode("/") + "&" +
 percentEncode(CanonicalizedQueryString)        
其中 HTTPMethod 是提交请求用的 HTTP 方法，比如 GET 和 POST。

percentEncode(“/”)是按照 1.b 中描述的 URL 编码规则对字符 /进行编码得到的值，即 %2F。

percentEncode (CanonicalizedQueryString) 是对第 1 步中构造的规范化请求字符串，再次按 1.b 中描述的 URL 编码规则进行编码后得到的字符串。注意：这里的 percentEncode (CanonicalizedQueryString) 是两次编码后得到的结果。

计算 HMAC 值。

按照 RFC2104 的定义，使用步骤 2 得到的字符串 StringToSign 计算签名 HMAC 值。

注意：计算签名时，使用的 Key 就是您的 AccessKeySecret 加上一个 & 字符 (ASCII:38)，使用的哈希算法是 SHA1。

计算签名值。

按照 Base64 编码规则，把步骤 3 得到的 HMAC 值编码成字符串，即得到签名值（Signature）。

将得到的签名值作为 Signature 参数添加到请求参数中，即完成请求签名过程。

注意：得到的签名值作为请求参数值提交给邮件推送服务器的时候，要和其他参数一样，按照 RFC3986 的规则进行 URL 编码）。

签名示例
以 SingleSendMail 接口，通过 HTTPS 发送 POST 请求调用说明为例：

请求URL为：http://dm.aliyuncs.com/ 。

请求参数为：

 
AccessKeyId=testid&AccountName=<a%b'>&Action=SingleSendMail&AddressType=1&Format=XML&HtmlBody=4&RegionId=cn-hangzhou&ReplyToAddress=true&SignatureMethod=HMAC-SHA1&SignatureNonce=c1b2c332-4cfb-4a0f-b8cc-ebe622aa0a5c&SignatureVersion=1.0&Subject=3&TagName=2&Timestamp=2016-10-20T06:27:56Z&ToAddress=<EMAIL>&Version=2015-11-23
那么 StringToSign 就是

 
POST&%2F&AccessKeyId%3Dtestid%26AccountName%3D%253Ca%2525b%2527%253E%26Action%3DSingleSendMail%26AddressType%3D1%26Format%3DXML%26HtmlBody%3D4%26RegionId%3Dcn-hangzhou%26ReplyToAddress%3Dtrue%26SignatureMethod%3DHMAC-SHA1%26SignatureNonce%3Dc1b2c332-4cfb-4a0f-b8cc-ebe622aa0a5c%26SignatureVersion%3D1.0%26Subject%3D3%26TagName%3D2%26Timestamp%3D2016-10-20T06%253A27%253A56Z%26ToAddress%3D1%2540test.com%26Version%3D2015-11-23
假如使用的 AccessKeyId 是 testid，AccessKeySecret 是 testsecret，用于计算 HMAC 的 Key 就是 testsecret&，则计算得到的签名值是：

 
llJfXJjBW3OacrVgxxsITgYaYm0=
签名后，请求 http://dm.aliyuncs.com/ 发起 POST 请求的 BODY内容参数如下（增加了 Signature 参数和修改了请求头 Content-Type: application/x-www-form-urlencoded）：

 
Signature=llJfXJjBW3OacrVgxxsITgYaYm0=&AccessKeyId=testid&AccountName=<a%b'>&Action=SingleSendMail&AddressType=1&Format=XML&HtmlBody=4&RegionId=cn-hangzhou&ReplyToAddress=true&SignatureMethod=HMAC-SHA1&SignatureNonce=c1b2c332-4cfb-4a0f-b8cc-ebe622aa0a5c&SignatureVersion=1.0&Subject=3&TagName=2&Timestamp=2016-10-20T06:27:56Z&ToAddress=<EMAIL>&Version=2015-11-23
以上提供的是编码之前的BODY所需参数，具体请求需要对参数进行编码，详情请参考请求示例。






采用get请求，不要使用sdk



